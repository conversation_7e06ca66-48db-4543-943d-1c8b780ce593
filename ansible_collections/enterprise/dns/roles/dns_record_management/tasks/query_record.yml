---
# Query DNS record operations
# Comprehensive DNS record querying and information gathering

- name: Query specific DNS record
  ansible.windows.win_shell: |
    try {
        $records = Get-DnsServerResourceRecord -ZoneName "{{ validated_zone }}" -Name "{{ record_name | default('*') }}" -RRType "{{ record_type | default('ALL') }}" -ErrorAction SilentlyContinue
        
        if ($records) {
            Write-Output "FOUND"
            Write-Output "Record Count: $($records.Count)"
            
            foreach ($record in $records) {
                Write-Output "--- Record Details ---"
                Write-Output "Name: $($record.HostName)"
                Write-Output "Type: $($record.RecordType)"
                Write-Output "TTL: $($record.TimeToLive.TotalSeconds)"
                Write-Output "Data: $($record.RecordData)"
                Write-Output "Timestamp: $($record.Timestamp)"
                Write-Output "--- End Record ---"
            }
        } else {
            Write-Output "NOT_FOUND"
            Write-Output "No records found matching criteria"
        }
        exit 0
    } catch {
        Write-Output "ERROR: Failed to query DNS records: $($_.Exception.Message)"
        exit 1
    }
  register: dns_query_result
  failed_when: dns_query_result.rc != 0

- name: Query all records in zone (when no specific record requested)
  ansible.windows.win_shell: |
    try {
        $allRecords = Get-DnsServerResourceRecord -ZoneName "{{ validated_zone }}" -ErrorAction Stop
        
        Write-Output "ZONE_QUERY"
        Write-Output "Total Records: $($allRecords.Count)"
        
        # Group by record type
        $recordTypes = $allRecords | Group-Object RecordType
        foreach ($type in $recordTypes) {
            Write-Output "Type $($type.Name): $($type.Count) records"
        }
        
        # Show first 10 records as sample
        Write-Output "--- Sample Records (First 10) ---"
        $allRecords | Select-Object -First 10 | ForEach-Object {
            Write-Output "$($_.HostName) $($_.RecordType) $($_.RecordData)"
        }
        
        exit 0
    } catch {
        Write-Output "ERROR: Failed to query zone records: $($_.Exception.Message)"
        exit 1
    }
  register: dns_zone_query_result
  when: record_name is not defined or record_name == ""
  failed_when: dns_zone_query_result.rc != 0

- name: Parse query results
  set_fact:
    query_found_records: "{{ 'FOUND' in dns_query_result.stdout or 'ZONE_QUERY' in (dns_zone_query_result.stdout | default('')) }}"
    query_result_details: "{{ dns_query_result.stdout_lines | default([]) + (dns_zone_query_result.stdout_lines | default([])) }}"

- name: Extract record information
  set_fact:
    parsed_records: []
  when: query_found_records

- name: Process found records
  set_fact:
    parsed_records: "{{ parsed_records | default([]) + [item] }}"
  loop: "{{ query_result_details }}"
  when: 
    - query_found_records
    - "'---' not in item"
    - item | length > 0

- name: Query DNS server statistics
  ansible.windows.win_shell: |
    try {
        $stats = Get-DnsServerStatistics -ErrorAction Stop
        Write-Output "--- DNS Server Statistics ---"
        Write-Output "Queries Received: $($stats.QueryStatistics.QueriesReceived)"
        Write-Output "Queries Successful: $($stats.QueryStatistics.QueriesSuccessful)"
        Write-Output "Queries Failed: $($stats.QueryStatistics.QueriesFailed)"
        Write-Output "Zone Transfer Requests: $($stats.ZoneTransferStatistics.RequestReceived)"
        Write-Output "Dynamic Updates: $($stats.UpdateStatistics.DynamicUpdateReceived)"
        exit 0
    } catch {
        Write-Output "WARNING: Could not retrieve DNS server statistics: $($_.Exception.Message)"
        exit 0
    }
  register: dns_server_stats
  when: include_server_stats | default(false)

- name: Query zone information
  ansible.windows.win_shell: |
    try {
        $zone = Get-DnsServerZone -Name "{{ validated_zone }}" -ErrorAction Stop
        Write-Output "--- Zone Information ---"
        Write-Output "Zone Name: $($zone.ZoneName)"
        Write-Output "Zone Type: $($zone.ZoneType)"
        Write-Output "Zone File: $($zone.ZoneFile)"
        Write-Output "Dynamic Update: $($zone.DynamicUpdate)"
        Write-Output "Secure Update: $($zone.SecureSecondaries)"
        Write-Output "Master Servers: $($zone.MasterServers -join ', ')"
        Write-Output "Secondary Servers: $($zone.SecondaryServers -join ', ')"
        exit 0
    } catch {
        Write-Output "ERROR: Failed to get zone information: $($_.Exception.Message)"
        exit 1
    }
  register: dns_zone_info
  when: include_zone_info | default(true)

- name: Log query operation result
  set_fact:
    dns_operation_log: "{{ dns_operation_log | combine({
      'operation_result': {
        'records_found': query_found_records,
        'record_count': query_result_details | length,
        'query_type': 'specific' if record_name is defined and record_name != '' else 'zone_wide',
        'completed_at': ansible_date_time.iso8601
      }
    }) }}"

- name: Display query results
  ansible.builtin.debug:
    msg:
      - "DNS Query Results:"
      - "Zone: {{ validated_zone }}"
      - "Query Type: {{ 'Specific Record' if record_name is defined and record_name != '' else 'Zone-wide Query' }}"
      - "Records Found: {{ query_found_records }}"
      - "Total Results: {{ query_result_details | length }}"
      - "Query Details: {{ query_result_details[:5] if query_result_details | length > 5 else query_result_details }}"

- name: Save detailed query results to file
  ansible.builtin.copy:
    content: |
      DNS Query Results - {{ ansible_date_time.iso8601 }}
      ================================================
      Zone: {{ validated_zone }}
      Query Type: {{ 'Specific Record' if record_name is defined and record_name != '' else 'Zone-wide Query' }}
      Records Found: {{ query_found_records }}
      
      {% for line in query_result_details %}
      {{ line }}
      {% endfor %}
      
      {% if dns_zone_info is defined and dns_zone_info.stdout_lines is defined %}
      Zone Information:
      {% for line in dns_zone_info.stdout_lines %}
      {{ line }}
      {% endfor %}
      {% endif %}
      
      {% if dns_server_stats is defined and dns_server_stats.stdout_lines is defined %}
      Server Statistics:
      {% for line in dns_server_stats.stdout_lines %}
      {{ line }}
      {% endfor %}
      {% endif %}
    dest: "/tmp/dns_query_{{ dns_operation_id }}.txt"
  delegate_to: localhost
  when: save_query_results | default(true)
